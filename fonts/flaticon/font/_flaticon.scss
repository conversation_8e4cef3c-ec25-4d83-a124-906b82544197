    /*
    Flaticon icon font: Flaticon
    Creation date: 27/12/2020 05:48
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-financial:before { content: "\f100"; }
.flaticon-security:before { content: "\f101"; }
.flaticon-savings:before { content: "\f102"; }
.flaticon-phone-call:before { content: "\f103"; }
.flaticon-money:before { content: "\f104"; }
.flaticon-pie-chart:before { content: "\f105"; }
    
    $font-Flaticon-financial: "\f100";
    $font-Flaticon-security: "\f101";
    $font-Flaticon-savings: "\f102";
    $font-Flaticon-phone-call: "\f103";
    $font-Flaticon-money: "\f104";
    $font-Flaticon-pie-chart: "\f105";