{"name": "untree_co-financing", "firstRun": false, "exportConfig": true, "fileConfigs": [], "fileTree": {"expandedDirs": [], "hideSystemFiles": true, "systemFiles": [".*", "desktop.ini", "prepros.config", "$RECYCLE.BIN", "prepros.cfg", "prepros-6.config", "Prepros Export"], "hideUnwatchedFiles": false}, "imports": [{"path": "scss/style.scss", "imports": ["scss/bootstrap/_mixins.scss", "scss/bootstrap/_functions.scss", "scss/bootstrap/_variables.scss", "scss/bootstrap/bootstrap.scss", "scss/components/_site-navbar.scss", "scss/components/_site-base.scss", "scss/components/_site-blocks.scss", "scss/components/_site-footer.scss", "scss/bootstrap/vendor/_rfs.scss", "scss/bootstrap/mixins/_deprecate.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_color-scheme.scss", "scss/bootstrap/mixins/_image.scss", "scss/bootstrap/mixins/_resize.scss", "scss/bootstrap/mixins/_visually-hidden.scss", "scss/bootstrap/mixins/_reset-text.scss", "scss/bootstrap/mixins/_text-truncate.scss", "scss/bootstrap/mixins/_utilities.scss", "scss/bootstrap/mixins/_alert.scss", "scss/bootstrap/mixins/_backdrop.scss", "scss/bootstrap/mixins/_buttons.scss", "scss/bootstrap/mixins/_caret.scss", "scss/bootstrap/mixins/_pagination.scss", "scss/bootstrap/mixins/_lists.scss", "scss/bootstrap/mixins/_list-group.scss", "scss/bootstrap/mixins/_forms.scss", "scss/bootstrap/mixins/_table-variants.scss", "scss/bootstrap/mixins/_border-radius.scss", "scss/bootstrap/mixins/_box-shadow.scss", "scss/bootstrap/mixins/_gradients.scss", "scss/bootstrap/mixins/_transition.scss", "scss/bootstrap/mixins/_clearfix.scss", "scss/bootstrap/mixins/_container.scss", "scss/bootstrap/mixins/_grid.scss", "scss/bootstrap/_utilities.scss", "scss/bootstrap/_root.scss", "scss/bootstrap/_reboot.scss", "scss/bootstrap/_type.scss", "scss/bootstrap/_images.scss", "scss/bootstrap/_containers.scss", "scss/bootstrap/_grid.scss", "scss/bootstrap/_tables.scss", "scss/bootstrap/_forms.scss", "scss/bootstrap/_buttons.scss", "scss/bootstrap/_transitions.scss", "scss/bootstrap/_dropdown.scss", "scss/bootstrap/_button-group.scss", "scss/bootstrap/_nav.scss", "scss/bootstrap/_navbar.scss", "scss/bootstrap/_card.scss", "scss/bootstrap/_accordion.scss", "scss/bootstrap/_breadcrumb.scss", "scss/bootstrap/_pagination.scss", "scss/bootstrap/_badge.scss", "scss/bootstrap/_alert.scss", "scss/bootstrap/_progress.scss", "scss/bootstrap/_list-group.scss", "scss/bootstrap/_close.scss", "scss/bootstrap/_toasts.scss", "scss/bootstrap/_modal.scss", "scss/bootstrap/_tooltip.scss", "scss/bootstrap/_popover.scss", "scss/bootstrap/_carousel.scss", "scss/bootstrap/_spinners.scss", "scss/bootstrap/_offcanvas.scss", "scss/bootstrap/_placeholders.scss", "scss/bootstrap/_helpers.scss", "scss/bootstrap/utilities/_api.scss", "scss/bootstrap/forms/_labels.scss", "scss/bootstrap/forms/_form-text.scss", "scss/bootstrap/forms/_form-control.scss", "scss/bootstrap/forms/_form-select.scss", "scss/bootstrap/forms/_form-check.scss", "scss/bootstrap/forms/_form-range.scss", "scss/bootstrap/forms/_floating-labels.scss", "scss/bootstrap/forms/_input-group.scss", "scss/bootstrap/forms/_validation.scss", "scss/bootstrap/helpers/_clearfix.scss", "scss/bootstrap/helpers/_colored-links.scss", "scss/bootstrap/helpers/_ratio.scss", "scss/bootstrap/helpers/_position.scss", "scss/bootstrap/helpers/_stacks.scss", "scss/bootstrap/helpers/_visually-hidden.scss", "scss/bootstrap/helpers/_stretched-link.scss", "scss/bootstrap/helpers/_text-truncation.scss", "scss/bootstrap/helpers/_vr.scss"]}, {"path": "scss/bootstrap/_forms.scss", "imports": ["scss/bootstrap/forms/_labels.scss", "scss/bootstrap/forms/_form-text.scss", "scss/bootstrap/forms/_form-control.scss", "scss/bootstrap/forms/_form-select.scss", "scss/bootstrap/forms/_form-check.scss", "scss/bootstrap/forms/_form-range.scss", "scss/bootstrap/forms/_floating-labels.scss", "scss/bootstrap/forms/_input-group.scss", "scss/bootstrap/forms/_validation.scss"]}, {"path": "scss/bootstrap/_helpers.scss", "imports": ["scss/bootstrap/helpers/_clearfix.scss", "scss/bootstrap/helpers/_colored-links.scss", "scss/bootstrap/helpers/_ratio.scss", "scss/bootstrap/helpers/_position.scss", "scss/bootstrap/helpers/_stacks.scss", "scss/bootstrap/helpers/_visually-hidden.scss", "scss/bootstrap/helpers/_stretched-link.scss", "scss/bootstrap/helpers/_text-truncation.scss", "scss/bootstrap/helpers/_vr.scss"]}, {"path": "scss/bootstrap/_mixins.scss", "imports": ["scss/bootstrap/vendor/_rfs.scss", "scss/bootstrap/mixins/_deprecate.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_color-scheme.scss", "scss/bootstrap/mixins/_image.scss", "scss/bootstrap/mixins/_resize.scss", "scss/bootstrap/mixins/_visually-hidden.scss", "scss/bootstrap/mixins/_reset-text.scss", "scss/bootstrap/mixins/_text-truncate.scss", "scss/bootstrap/mixins/_utilities.scss", "scss/bootstrap/mixins/_alert.scss", "scss/bootstrap/mixins/_backdrop.scss", "scss/bootstrap/mixins/_buttons.scss", "scss/bootstrap/mixins/_caret.scss", "scss/bootstrap/mixins/_pagination.scss", "scss/bootstrap/mixins/_lists.scss", "scss/bootstrap/mixins/_list-group.scss", "scss/bootstrap/mixins/_forms.scss", "scss/bootstrap/mixins/_table-variants.scss", "scss/bootstrap/mixins/_border-radius.scss", "scss/bootstrap/mixins/_box-shadow.scss", "scss/bootstrap/mixins/_gradients.scss", "scss/bootstrap/mixins/_transition.scss", "scss/bootstrap/mixins/_clearfix.scss", "scss/bootstrap/mixins/_container.scss", "scss/bootstrap/mixins/_grid.scss"]}, {"path": "scss/bootstrap/bootstrap-grid.scss", "imports": ["scss/bootstrap/_functions.scss", "scss/bootstrap/_variables.scss", "scss/bootstrap/mixins/_lists.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_container.scss", "scss/bootstrap/mixins/_grid.scss", "scss/bootstrap/mixins/_utilities.scss", "scss/bootstrap/vendor/_rfs.scss", "scss/bootstrap/_root.scss", "scss/bootstrap/_containers.scss", "scss/bootstrap/_grid.scss", "scss/bootstrap/_utilities.scss", "scss/bootstrap/utilities/_api.scss"]}, {"path": "scss/bootstrap/bootstrap-reboot.scss", "imports": ["scss/bootstrap/_functions.scss", "scss/bootstrap/_variables.scss", "scss/bootstrap/_mixins.scss", "scss/bootstrap/_root.scss", "scss/bootstrap/_reboot.scss", "scss/bootstrap/vendor/_rfs.scss", "scss/bootstrap/mixins/_deprecate.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_color-scheme.scss", "scss/bootstrap/mixins/_image.scss", "scss/bootstrap/mixins/_resize.scss", "scss/bootstrap/mixins/_visually-hidden.scss", "scss/bootstrap/mixins/_reset-text.scss", "scss/bootstrap/mixins/_text-truncate.scss", "scss/bootstrap/mixins/_utilities.scss", "scss/bootstrap/mixins/_alert.scss", "scss/bootstrap/mixins/_backdrop.scss", "scss/bootstrap/mixins/_buttons.scss", "scss/bootstrap/mixins/_caret.scss", "scss/bootstrap/mixins/_pagination.scss", "scss/bootstrap/mixins/_lists.scss", "scss/bootstrap/mixins/_list-group.scss", "scss/bootstrap/mixins/_forms.scss", "scss/bootstrap/mixins/_table-variants.scss", "scss/bootstrap/mixins/_border-radius.scss", "scss/bootstrap/mixins/_box-shadow.scss", "scss/bootstrap/mixins/_gradients.scss", "scss/bootstrap/mixins/_transition.scss", "scss/bootstrap/mixins/_clearfix.scss", "scss/bootstrap/mixins/_container.scss", "scss/bootstrap/mixins/_grid.scss"]}, {"path": "scss/bootstrap/bootstrap-utilities.scss", "imports": ["scss/bootstrap/_functions.scss", "scss/bootstrap/_variables.scss", "scss/bootstrap/_mixins.scss", "scss/bootstrap/_utilities.scss", "scss/bootstrap/_helpers.scss", "scss/bootstrap/utilities/_api.scss", "scss/bootstrap/vendor/_rfs.scss", "scss/bootstrap/mixins/_deprecate.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_color-scheme.scss", "scss/bootstrap/mixins/_image.scss", "scss/bootstrap/mixins/_resize.scss", "scss/bootstrap/mixins/_visually-hidden.scss", "scss/bootstrap/mixins/_reset-text.scss", "scss/bootstrap/mixins/_text-truncate.scss", "scss/bootstrap/mixins/_utilities.scss", "scss/bootstrap/mixins/_alert.scss", "scss/bootstrap/mixins/_backdrop.scss", "scss/bootstrap/mixins/_buttons.scss", "scss/bootstrap/mixins/_caret.scss", "scss/bootstrap/mixins/_pagination.scss", "scss/bootstrap/mixins/_lists.scss", "scss/bootstrap/mixins/_list-group.scss", "scss/bootstrap/mixins/_forms.scss", "scss/bootstrap/mixins/_table-variants.scss", "scss/bootstrap/mixins/_border-radius.scss", "scss/bootstrap/mixins/_box-shadow.scss", "scss/bootstrap/mixins/_gradients.scss", "scss/bootstrap/mixins/_transition.scss", "scss/bootstrap/mixins/_clearfix.scss", "scss/bootstrap/mixins/_container.scss", "scss/bootstrap/mixins/_grid.scss", "scss/bootstrap/helpers/_clearfix.scss", "scss/bootstrap/helpers/_colored-links.scss", "scss/bootstrap/helpers/_ratio.scss", "scss/bootstrap/helpers/_position.scss", "scss/bootstrap/helpers/_stacks.scss", "scss/bootstrap/helpers/_visually-hidden.scss", "scss/bootstrap/helpers/_stretched-link.scss", "scss/bootstrap/helpers/_text-truncation.scss", "scss/bootstrap/helpers/_vr.scss"]}, {"path": "scss/bootstrap/bootstrap.scss", "imports": ["scss/bootstrap/_functions.scss", "scss/bootstrap/_variables.scss", "scss/bootstrap/_mixins.scss", "scss/bootstrap/_utilities.scss", "scss/bootstrap/_root.scss", "scss/bootstrap/_reboot.scss", "scss/bootstrap/_type.scss", "scss/bootstrap/_images.scss", "scss/bootstrap/_containers.scss", "scss/bootstrap/_grid.scss", "scss/bootstrap/_tables.scss", "scss/bootstrap/_forms.scss", "scss/bootstrap/_buttons.scss", "scss/bootstrap/_transitions.scss", "scss/bootstrap/_dropdown.scss", "scss/bootstrap/_button-group.scss", "scss/bootstrap/_nav.scss", "scss/bootstrap/_navbar.scss", "scss/bootstrap/_card.scss", "scss/bootstrap/_accordion.scss", "scss/bootstrap/_breadcrumb.scss", "scss/bootstrap/_pagination.scss", "scss/bootstrap/_badge.scss", "scss/bootstrap/_alert.scss", "scss/bootstrap/_progress.scss", "scss/bootstrap/_list-group.scss", "scss/bootstrap/_close.scss", "scss/bootstrap/_toasts.scss", "scss/bootstrap/_modal.scss", "scss/bootstrap/_tooltip.scss", "scss/bootstrap/_popover.scss", "scss/bootstrap/_carousel.scss", "scss/bootstrap/_spinners.scss", "scss/bootstrap/_offcanvas.scss", "scss/bootstrap/_placeholders.scss", "scss/bootstrap/_helpers.scss", "scss/bootstrap/utilities/_api.scss", "scss/bootstrap/vendor/_rfs.scss", "scss/bootstrap/mixins/_deprecate.scss", "scss/bootstrap/mixins/_breakpoints.scss", "scss/bootstrap/mixins/_color-scheme.scss", "scss/bootstrap/mixins/_image.scss", "scss/bootstrap/mixins/_resize.scss", "scss/bootstrap/mixins/_visually-hidden.scss", "scss/bootstrap/mixins/_reset-text.scss", "scss/bootstrap/mixins/_text-truncate.scss", "scss/bootstrap/mixins/_utilities.scss", "scss/bootstrap/mixins/_alert.scss", "scss/bootstrap/mixins/_backdrop.scss", "scss/bootstrap/mixins/_buttons.scss", "scss/bootstrap/mixins/_caret.scss", "scss/bootstrap/mixins/_pagination.scss", "scss/bootstrap/mixins/_lists.scss", "scss/bootstrap/mixins/_list-group.scss", "scss/bootstrap/mixins/_forms.scss", "scss/bootstrap/mixins/_table-variants.scss", "scss/bootstrap/mixins/_border-radius.scss", "scss/bootstrap/mixins/_box-shadow.scss", "scss/bootstrap/mixins/_gradients.scss", "scss/bootstrap/mixins/_transition.scss", "scss/bootstrap/mixins/_clearfix.scss", "scss/bootstrap/mixins/_container.scss", "scss/bootstrap/mixins/_grid.scss", "scss/bootstrap/forms/_labels.scss", "scss/bootstrap/forms/_form-text.scss", "scss/bootstrap/forms/_form-control.scss", "scss/bootstrap/forms/_form-select.scss", "scss/bootstrap/forms/_form-check.scss", "scss/bootstrap/forms/_form-range.scss", "scss/bootstrap/forms/_floating-labels.scss", "scss/bootstrap/forms/_input-group.scss", "scss/bootstrap/forms/_validation.scss", "scss/bootstrap/helpers/_clearfix.scss", "scss/bootstrap/helpers/_colored-links.scss", "scss/bootstrap/helpers/_ratio.scss", "scss/bootstrap/helpers/_position.scss", "scss/bootstrap/helpers/_stacks.scss", "scss/bootstrap/helpers/_visually-hidden.scss", "scss/bootstrap/helpers/_stretched-link.scss", "scss/bootstrap/helpers/_text-truncation.scss", "scss/bootstrap/helpers/_vr.scss"]}], "projectView": {"selectedView": "log"}, "fileWatcher": {"enabled": true, "watchedExtensions": ["less", "sass", "scss", "styl", "md", "markdown", "coffee", "js", "jade", "haml", "slim", "ls", "kit", "png", "jpg", "jpeg", "ts", "pug", "css", "html", "htm", "php"]}, "pathFilters": ["node_modules", ".*", "bower_components", "prepros.config", "Prepros Export", "prepros-6.config", "prepros.cfg", "wp-admin", "wp-includes"], "server": {"port": 8092, "assignNewPortAutomatically": true, "enable": true, "proxy": {"enable": false, "url": ""}}, "browser-sync": {"enable": false, "clicks": true, "forms": true, "scroll": true}, "live-reload": {"enable": true, "animate": true, "delay": 0}, "ftp-deploy": {"connectionType": "ftp", "remotePath": "", "uploadTimeout": 20000, "uploadOnChange": false, "ftp": {"secure": false, "keepAlive": true, "host": "", "port": 21, "user": "", "password": ""}, "sftp": {"host": "", "port": 22, "usePrivateKey": false, "username": "", "password": "", "privateKey": "", "passphrase": ""}, "pathFilters": ["config.rb", "prepros.config", "prepros-6.config", "node_modules", "Prepros Export", ".git", ".idea", ".sass-cache", ".hg", ".svn", ".cache", ".DS_Store", "*.sass", "*.scss", "*.less", "*.pug", "*.jade", "*.styl", "*.haml", "*.slim", "*.coffee", "*.ls", "*.kit", "*.ts"], "history": []}, "file-type-sass": "{\"compilers\":[\"node-sass\",\"autoprefixer\",\"minify-css\"]}", "file-type-less": "{\"compilers\":[\"less\",\"autoprefixer\",\"minify-css\"]}", "autoprefixer": {"browsers": "last 5 versions"}, "file-type-pug": "{\"compilers\":[\"pug\"]}", "file-type-css": "{\"compilers\":[\"autoprefixer\",\"cssnext\",\"minify-css\"]}", "file-type-javascript": "{\"compilers\":[\"concat-js\",\"babel\",\"uglify-js\"]}", "file-type-stylus": "{\"compilers\":[\"stylus\",\"autoprefixer\",\"minify-css\"]}", "file-type-markdown": "{\"compilers\":[\"markdown\"]}", "file-type-haml": "{\"compilers\":[\"haml\"]}", "file-type-slim": "{\"compilers\":[\"slim\"]}", "file-type-coffee-script": "{\"compilers\":[\"coffee-script\",\"uglify-js\"]}", "file-type-livescript": "{\"compilers\":[\"livescript\",\"uglify-js\"]}", "file-type-kit": "{\"compilers\":[\"kit\"]}", "uglify-js": {"ie8": false, "compress": {"sequences": true, "properties": true, "dead_code": true, "drop_debugger": true, "unsafe": false, "unsafe_comps": false, "unsafe_math": false, "unsafe_proto": false, "unsafe_regexp": false, "conditionals": true, "comparisons": true, "evaluate": true, "booleans": true, "loops": true, "unused": true, "toplevel": false, "top_retain": "", "hoist_funs": true, "hoist_vars": false, "if_return": true, "join_vars": true, "collapse_vars": true, "reduce_vars": true, "warnings": true, "negate_iife": true, "pure_getters": false, "pure_funcs": [], "drop_console": false, "expression": false, "keep_fargs": true, "keep_fnames": false, "passes": 1, "keep_infinity": false, "side_effects": true, "global_defs": []}, "output": {"ascii_only": false, "beautify": false, "comments": "", "indent_level": 4, "indent_start": 0, "inline_script": false, "keep_quoted_props": false, "max_line_len": false, "preamble": "", "preserve_line": false, "quote_keys": false, "quote_style": 0, "semicolons": true, "shebang": true, "width": 80}}, "cssnext": {"customProperties": true, "applyRule": true, "calc": false, "nesting": true, "customMedia": true, "mediaQueriesRange": true, "customSelectors": true, "attributeCaseInsensitive": true, "colorRebeccapurple": true, "colorHwb": true, "colorGray": true, "colorHexAlpha": true, "colorFunction": true, "fontVariant": true, "filter": true, "initial": true, "rem": true, "pseudoElements": true, "pseudoClassMatches": true, "pseudoClassNot": true, "pseudoClassAnyLink": true, "colorRgba": true, "overflowWrap": true}, "file-type-typescript": "{\"compilers\":[\"typescript\",\"uglify-js\"]}", "babel": {"useBabelRc": true, "presets": {"babel-preset-es2015": true}, "plugins": {"babel-plugin-syntax-jsx": true, "babel-plugin-transform-react-jsx": true, "babel-plugin-transform-async-to-generator": true, "babel-plugin-transform-class-properties": true, "babel-plugin-transform-object-rest-spread": true}}, "file-type-png": "{\"compilers\":[\"png\"],\"compiler-png\":{\"enabled\":true,\"originalSize\":0,\"newSize\":0}}", "file-type-jpg": "{\"compilers\":[\"jpg\"],\"compiler-jpg\":{\"enabled\":true,\"originalSize\":0,\"newSize\":0}}"}