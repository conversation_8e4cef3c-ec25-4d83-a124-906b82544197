body {
	font-family: $font-family-sans-serif;
	font-weight: 400;
	font-size: 15px;
	overflow-x: hidden;
	color: #848d92;
	position: relative;
	&:before {
		content: "";
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 9;
		background-color: rgba($black, .5);
		transition: .3s all ease;
		opacity: 0;
		visibility: hidden;
	}
	&.offcanvas-menu {
		&:before {
			opacity: 1;
			visibility: visible;
		}

	}
}
h1, h2, h3, h4, h5, .logo {
	font-family: $font-family-sans-serif-secondary;
}
a, .btn {
	transition: .3s all ease;
	text-decoration: none;
}

.btn {
	padding: 15px 20px;
	border-radius: 4px;
	border-color: none;
	font-size: 12px;
	font-weight: 600;
	letter-spacing: .1rem;
	text-transform: uppercase;
	&.active, &:focus {
		outline: none;
		box-shadow: none;
	}
	&.btn-primary {
		color: $white;
		background-color: $primary;
		&:hover {
			border-color: $primary;
		}
	}
	&.btn-outline-primary {
		color: $primary;
		&:hover {
			color: $white;
		}
	}
	&.btn-outline-white {
		border-color: $white;
		color: $white;
		&:hover {
			background-color: $white;
			color: $black;
		}
	}
	&.btn-outline-white-reverse {
		background-color: $white;
		border-color: $white;
		color: $primary;
		&:hover {
			background: transparent;
			border-color: $white;
			color: $white;
		}
	}
	&.btn-black {
		background: $black;
		color: $white;
		border-color: $black;
		&:hover {
			border-color: lighten($black, 5%);
			background: lighten($black, 5%);
			color: $white;
		}
	}
	&.has-arrow {
		padding-right: 50px;
		position: relative;
		[class^="icon-"] {
			position: absolute;
			right: 20px;
			top: 50%;
			transform: translateY(-50%);
		}
	}
}



.form-control {
	height: 54px;
	border-color: rgba($black, .1);
	background-color: transparent!important;
	margin-bottom: 10px;
	&:active, &:focus {
		outline: none;
		box-shadow: none;
		border-color: rgba($black, .2)!important;
	}
	&::-webkit-input-placeholder { 
		color: rgba($black, .7);
	}

	&::-moz-placeholder { 
		color: rgba($black, .7);
	}
	&:-ms-input-placeholder { 
		color: rgba($black, .7);
	}
	&:-moz-placeholder { 
		color: rgba($black, .7);
	}
}
textarea {
	height: auto!important;
}

// PRELOADER STYLE
#overlayer {
	width:100%;
	height:100%;  
	position:fixed;
	z-index:7100;
	background: $white;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.loader {
	z-index:7700;
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.relative {
	position: relative;
}